import cv2
def detect_light_sources(img_path, min_area=50, show_result=False):
    img = cv2.imread(img_path)
    if img is None:
        raise ValueError("Could not load image")
    
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    blurred = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(gray, (15, 15), 0)
    
    # Adaptive thresholding works better for varying light conditions
    thresh = cv2.adaptiveThreshold(
        blurred, 255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV, 11, 2
    )
    
    # Find contours
    contours, _ = cv2.findContours(
        thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
    )
    
    # Calculate light source positions
    light_sources = []
    for c in contours:
        area = cv2.contourArea(c)
        if area > min_area:
            M = cv2.moments(c)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                light_sources.append((cx, cy, area))
    
    # Sort by brightness (largest area first)
    light_sources.sort(key=lambda x: x[2], reverse=True)
    
    if show_result:
        debug_img = img.copy()
        for (cx, cy, area) in light_sources:
            cv2.circle(debug_img, (cx, cy), 10, (0, 0, 255), -1)
        cv2.imshow("Light Sources", debug_img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    
    return [(x, y) for (x, y, _) in light_sources]

# Usage
light_positions = detect_light_sources(
    'assets/background.jpg',
    min_area=100,
    show_result=False
)
print(f"Detected light sources: {light_positions}")