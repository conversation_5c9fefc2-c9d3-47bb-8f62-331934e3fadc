#!/usr/bin/env python3
"""
PHONE CAMERA REALISTIC Composite Generator
Creates images that look exactly like they were captured by a phone camera
Focuses on color accuracy, phone camera characteristics, and natural appearance
"""

import cv2
import numpy as np
import os

def analyze_background_color_profile(bg_img):
    """Analyze background color characteristics for perfect matching"""
    # Convert to different color spaces for comprehensive analysis
    bg_lab = cv2.cvtColor(bg_img, cv2.COLOR_BGR2LAB)
    bg_hsv = cv2.cvtColor(bg_img, cv2.COLOR_BGR2HSV)
    
    # Calculate color statistics
    lab_mean = np.mean(bg_lab.reshape(-1, 3), axis=0)
    lab_std = np.std(bg_lab.reshape(-1, 3), axis=0)
    
    hsv_mean = np.mean(bg_hsv.reshape(-1, 3), axis=0)
    hsv_std = np.std(bg_hsv.reshape(-1, 3), axis=0)
    
    # Analyze color temperature
    b, g, r = cv2.split(bg_img.astype(np.float32))
    color_temp_ratio = np.mean(b) / np.mean(r) if np.mean(r) > 0 else 1.0
    
    return {
        'lab_mean': lab_mean,
        'lab_std': lab_std,
        'hsv_mean': hsv_mean,
        'hsv_std': hsv_std,
        'color_temp_ratio': color_temp_ratio,
        'overall_brightness': np.mean(bg_img),
        'contrast': np.std(bg_img)
    }

def perfect_color_matching(person_img, bg_profile, person_alpha):
    """Perfect color matching to background characteristics"""
    person_mask = person_alpha > 0.1
    if not np.any(person_mask):
        return person_img
    
    # Convert person to LAB for precise color control
    person_lab = cv2.cvtColor(person_img, cv2.COLOR_BGR2LAB).astype(np.float32)
    
    # Get person color statistics
    person_pixels_lab = person_lab[person_mask]
    person_lab_mean = np.mean(person_pixels_lab, axis=0)
    person_lab_std = np.std(person_pixels_lab, axis=0)
    
    # Apply color correction
    for channel in range(3):
        if person_lab_std[channel] > 0:
            # Normalize and apply background characteristics
            person_channel = person_lab[:, :, channel]
            person_channel = (person_channel - person_lab_mean[channel]) / person_lab_std[channel]
            person_channel = person_channel * bg_profile['lab_std'][channel] + bg_profile['lab_mean'][channel]
            person_lab[:, :, channel] = person_channel
    
    # Convert back to BGR
    person_corrected = cv2.cvtColor(np.clip(person_lab, 0, 255).astype(np.uint8), cv2.COLOR_LAB2BGR)
    
    # Apply color temperature correction
    person_corrected = person_corrected.astype(np.float32)
    b, g, r = cv2.split(person_corrected)
    
    # Adjust blue/red ratio to match background
    current_ratio = np.mean(b[person_mask]) / np.mean(r[person_mask]) if np.mean(r[person_mask]) > 0 else 1.0
    temp_adjustment = bg_profile['color_temp_ratio'] / current_ratio if current_ratio > 0 else 1.0
    temp_adjustment = np.clip(temp_adjustment, 0.8, 1.2)  # Limit extreme adjustments
    
    if temp_adjustment > 1:
        b *= temp_adjustment
    else:
        r *= (1 / temp_adjustment)
    
    person_temp_corrected = cv2.merge([b, g, r])
    person_temp_corrected = np.clip(person_temp_corrected, 0, 255).astype(np.uint8)
    
    return person_temp_corrected

def apply_phone_camera_effects(person_img, bg_img, person_alpha):
    """Apply phone camera specific effects for realism"""
    person_mask = person_alpha > 0.1
    if not np.any(person_mask):
        return person_img
    
    # 1. Phone camera saturation boost
    person_hsv = cv2.cvtColor(person_img, cv2.COLOR_BGR2HSV).astype(np.float32)
    person_hsv[:, :, 1] *= 1.12  # Boost saturation like modern phones
    person_hsv[:, :, 1] = np.clip(person_hsv[:, :, 1], 0, 255)
    
    # 2. Phone camera contrast enhancement
    person_hsv[:, :, 2] = person_hsv[:, :, 2] * 1.08  # Slight brightness boost
    person_hsv[:, :, 2] = np.clip(person_hsv[:, :, 2], 0, 255)
    
    person_enhanced = cv2.cvtColor(person_hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)
    
    # 3. Phone camera sharpening (subtle)
    kernel = np.array([[-0.05, -0.1, -0.05],
                      [-0.1,   1.4, -0.1],
                      [-0.05, -0.1, -0.05]])
    person_sharpened = cv2.filter2D(person_enhanced.astype(np.float32), -1, kernel)
    person_sharpened = np.clip(person_sharpened, 0, 255).astype(np.uint8)
    
    # 4. Match background noise characteristics
    bg_gray = cv2.cvtColor(bg_img, cv2.COLOR_BGR2GRAY)
    bg_noise_level = np.std(bg_gray)
    
    # Add matching noise
    noise = np.random.normal(0, bg_noise_level * 0.15, person_sharpened.shape).astype(np.float32)
    person_final = person_sharpened.astype(np.float32) + noise
    person_final = np.clip(person_final, 0, 255).astype(np.uint8)
    
    # Apply only to person area
    result = person_img.copy()
    result[person_mask] = person_final[person_mask]
    
    return result

def create_natural_shadows(person_alpha, bg_shape, person_pos, bg_img):
    """Create natural shadows based on background lighting"""
    h, w = person_alpha.shape
    bg_h, bg_w = bg_shape[:2]
    x, y = person_pos
    
    # Analyze background lighting direction
    bg_gray = cv2.cvtColor(bg_img, cv2.COLOR_BGR2GRAY)
    
    # Simple lighting analysis - check brightness gradients
    grad_x = cv2.Sobel(bg_gray, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(bg_gray, cv2.CV_64F, 0, 1, ksize=3)
    
    avg_grad_x = np.mean(grad_x)
    avg_grad_y = np.mean(grad_y)
    
    # Determine shadow direction
    shadow_offset_x = int(np.sign(avg_grad_x) * 12)
    shadow_offset_y = int(abs(avg_grad_y) * 0.1 + 8)
    
    # Create shadow
    shadow_canvas = np.zeros((bg_h, bg_w), dtype=np.uint8)
    person_shadow = (person_alpha > 0.1).astype(np.uint8) * 160
    
    # Place shadow with calculated offset
    shadow_y = y + shadow_offset_y
    shadow_x = x + shadow_offset_x
    
    if (shadow_y >= 0 and shadow_x >= 0 and 
        shadow_y + h <= bg_h and shadow_x + w <= bg_w):
        shadow_canvas[shadow_y:shadow_y+h, shadow_x:shadow_x+w] = person_shadow
    
    # Blur shadow naturally
    shadow_canvas = cv2.GaussianBlur(shadow_canvas, (19, 19), 0)
    
    # Create contact shadow (darker, smaller)
    contact_shadow = np.zeros((bg_h, bg_w), dtype=np.uint8)
    contact_region = person_alpha[-10:, :] if h > 10 else person_alpha  # Bottom of person
    contact_mask = (contact_region > 0.1).astype(np.uint8) * 200
    
    if y + h - 5 < bg_h:
        contact_y = y + h - 5
        contact_shadow[contact_y:contact_y+min(5, bg_h-contact_y), x:x+w] = contact_mask[:min(5, bg_h-contact_y), :]
    
    contact_shadow = cv2.GaussianBlur(contact_shadow, (7, 7), 0)
    
    # Combine shadows
    combined_shadow = np.maximum(shadow_canvas, contact_shadow)
    
    return combined_shadow

def create_phone_realistic_composite(background_path, person_path, output_path):
    """
    Create a composite that looks exactly like it was captured by a phone camera
    """
    try:
        print(f"📱 Creating PHONE CAMERA REALISTIC composite...")
        
        # Load images
        bg = cv2.imread(background_path)
        person = cv2.imread(person_path, cv2.IMREAD_UNCHANGED)
        
        if bg is None or person is None:
            raise ValueError("Could not load images")
        
        print(f"📐 Background: {bg.shape[1]}x{bg.shape[0]}")
        print(f"👤 Person: {person.shape[1]}x{person.shape[0]}")
        
        # Extract alpha channel
        if person.shape[2] == 4:
            person_alpha = person[:, :, 3] / 255.0
            person_rgb = person[:, :, :3]
        else:
            person_alpha = np.ones(person.shape[:2], dtype=np.float32)
            person_rgb = person
        
        # 1. REALISTIC PHONE CAMERA SCALING
        bg_h, bg_w = bg.shape[:2]
        # Phone cameras typically capture people at 20-30% of frame height
        target_scale = 0.27  # Realistic phone camera scale
        new_h = int(bg_h * target_scale)
        new_w = int(new_h * person.shape[1] / person.shape[0])
        
        # High-quality resize
        person_rgb = cv2.resize(person_rgb, (new_w, new_h), interpolation=cv2.INTER_LANCZOS4)
        person_alpha = cv2.resize(person_alpha, (new_w, new_h), interpolation=cv2.INTER_LANCZOS4)
        
        print(f"🔄 Phone camera scale: {new_w}x{new_h}")
        
        # 2. NATURAL PHONE CAMERA POSITIONING
        # Phone cameras typically place subjects slightly off-center
        y = bg_h - new_h - 40  # Leave some ground space
        x = int(bg_w * 0.45)   # Slightly off-center for natural look
        
        # Ensure bounds
        y = max(0, min(y, bg_h - new_h))
        x = max(0, min(x, bg_w - new_w))
        
        print(f"📍 Natural position: ({x}, {y})")
        
        # 3. ANALYZE BACKGROUND COLOR PROFILE
        bg_profile = analyze_background_color_profile(bg)
        print(f"🎨 Background analysis - Brightness: {bg_profile['overall_brightness']:.1f}, Temp ratio: {bg_profile['color_temp_ratio']:.2f}")
        
        # 4. PERFECT COLOR MATCHING
        person_rgb = perfect_color_matching(person_rgb, bg_profile, person_alpha)
        print("🌈 Applied perfect color matching")
        
        # 5. PHONE CAMERA EFFECTS
        person_rgb = apply_phone_camera_effects(person_rgb, bg, person_alpha)
        print("📸 Applied phone camera effects")
        
        # 6. NATURAL SHADOWS
        shadow = create_natural_shadows(person_alpha, bg.shape, (x, y), bg)
        
        # Apply shadows to background
        shadow_3d = shadow[:, :, np.newaxis] / 255.0
        bg = bg.astype(np.float32)
        bg = bg * (1 - shadow_3d * 0.35)  # Natural shadow darkness
        bg = bg.astype(np.uint8)
        print("🌑 Added natural shadows")
        
        # 7. EDGE SOFTENING (phone cameras have slight softness)
        person_alpha_soft = cv2.GaussianBlur(person_alpha, (3, 3), 0)
        print("✨ Applied phone camera edge softening")
        
        # 8. FINAL PHONE-REALISTIC COMPOSITING
        person_region = bg[y:y+new_h, x:x+new_w]
        alpha_3d = person_alpha_soft[:, :, np.newaxis]
        
        # Natural blending
        blended = alpha_3d * person_rgb.astype(np.float32) + (1 - alpha_3d) * person_region.astype(np.float32)
        bg[y:y+new_h, x:x+new_w] = blended.astype(np.uint8)
        
        # 9. FINAL PHONE CAMERA POST-PROCESSING
        # Slight overall saturation boost (phone characteristic)
        final_hsv = cv2.cvtColor(bg, cv2.COLOR_BGR2HSV).astype(np.float32)
        final_hsv[:, :, 1] *= 1.05  # Subtle saturation boost
        final_hsv[:, :, 1] = np.clip(final_hsv[:, :, 1], 0, 255)
        bg = cv2.cvtColor(final_hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)
        
        # Save result
        cv2.imwrite(output_path, bg)
        print(f"✅ PHONE REALISTIC composite saved: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Create phone camera realistic composite"""
    print("📱 PHONE CAMERA REALISTIC COMPOSITE GENERATOR")
    print("=" * 60)
    
    if not os.path.exists('assets/person_removed_ml.png'):
        print("❌ Person cutout not found!")
        return
    
    # Create the ultimate phone camera realistic composite
    print(f"\n🎯 Creating PHONE CAMERA REALISTIC composite...")
    success = create_phone_realistic_composite(
        'assets/background.jpg',
        'assets/person_removed_ml.png',
        'assets/PHONE_CAMERA_REALISTIC.jpg'
    )
    
    if success:
        print(f"\n🎉 PHONE CAMERA REALISTIC COMPOSITE COMPLETE!")
        print("📁 File: assets/PHONE_CAMERA_REALISTIC.jpg")
        print("📱 This should look EXACTLY like it was captured by a phone camera!")
        print("🔥 Perfect color matching and natural phone camera characteristics!")
    else:
        print(f"\n❌ Failed to create phone realistic composite")

if __name__ == "__main__":
    main()
