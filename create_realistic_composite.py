#!/usr/bin/env python3
"""
Enhanced Flam Project - Realistic Person Placement
Creates highly realistic composite images with intelligent positioning and scaling
"""

import cv2
import numpy as np
import os

def analyze_background_for_placement(bg_img):
    """Analyze background to find optimal placement positions"""
    height, width = bg_img.shape[:2]
    
    # Convert to grayscale for analysis
    gray = cv2.cvtColor(bg_img, cv2.COLOR_BGR2GRAY)
    
    # Find areas with less detail (smoother areas) for better placement
    blur = cv2.GaussianBlur(gray, (21, 21), 0)
    edges = cv2.Canny(blur, 50, 150)
    
    # Divide image into grid and find areas with fewer edges
    grid_size = 6
    h_step = height // grid_size
    w_step = width // grid_size
    
    best_positions = []
    
    for i in range(grid_size):
        for j in range(grid_size):
            y_start = i * h_step
            y_end = min((i + 1) * h_step, height)
            x_start = j * w_step
            x_end = min((j + 1) * w_step, width)
            
            # Count edges in this region
            region_edges = edges[y_start:y_end, x_start:x_end]
            edge_density = np.sum(region_edges) / (region_edges.shape[0] * region_edges.shape[1])
            
            # Prefer bottom areas and areas with lower edge density
            bottom_preference = (height - y_start) / height
            center_preference = 1 - abs((x_start + w_step/2) - width/2) / (width/2)
            score = bottom_preference * 0.5 + (1 - edge_density / 255) * 0.3 + center_preference * 0.2
            
            best_positions.append((x_start, y_start, score))
    
    # Sort by score and return best positions
    best_positions.sort(key=lambda x: x[2], reverse=True)
    return best_positions[:5]

def smart_resize_person(fg, bg, target_scale=0.25):
    """Intelligently resize person based on background size"""
    bg_height, bg_width = bg.shape[:2]
    fg_height, fg_width = fg.shape[:2]
    
    # Calculate realistic person size
    target_height = int(bg_height * target_scale)
    aspect_ratio = fg_width / fg_height
    target_width = int(target_height * aspect_ratio)
    
    # Ensure person isn't too wide
    if target_width > bg_width * 0.4:
        target_width = int(bg_width * 0.4)
        target_height = int(target_width / aspect_ratio)
    
    # Resize with high quality
    fg_resized = cv2.resize(fg, (target_width, target_height), interpolation=cv2.INTER_LANCZOS4)
    return fg_resized

def create_simple_shadow(person_mask, offset_x=10, offset_y=15, blur_size=15):
    """Create a simple realistic shadow"""
    # Create shadow by shifting the mask
    shadow = np.zeros_like(person_mask, dtype=np.uint8)
    
    h, w = person_mask.shape
    
    # Shift the mask to create shadow
    if offset_y > 0 and offset_x >= 0:
        shadow[offset_y:, offset_x:] = person_mask[:-offset_y, :-offset_x] if offset_x > 0 else person_mask[:-offset_y, :]
    
    # Blur the shadow
    shadow = cv2.GaussianBlur(shadow, (blur_size, blur_size), 0)
    
    # Make shadow semi-transparent
    shadow = (shadow * 0.4).astype(np.uint8)
    
    return shadow

def create_realistic_composite(background_path, person_path, output_path, 
                             person_scale=0.28, position="auto", add_shadow=True):
    """
    Create a realistic composite image with intelligent placement
    
    Args:
        background_path: Path to background image
        person_path: Path to person cutout (PNG with transparency)
        output_path: Path for output composite
        person_scale: Scale of person relative to background height (0.15-0.4)
        position: "auto", "left", "center", "right", or (x, y) tuple
        add_shadow: Whether to add a simple shadow
    """
    try:
        # Load images
        bg = cv2.imread(background_path)
        fg = cv2.imread(person_path, cv2.IMREAD_UNCHANGED)
        
        if bg is None:
            raise ValueError(f"Could not load background image: {background_path}")
        if fg is None:
            raise ValueError(f"Could not load person image: {person_path}")
        
        print(f"📐 Background: {bg.shape[1]}x{bg.shape[0]}")
        print(f"👤 Person: {fg.shape[1]}x{fg.shape[0]}")
        
        # Smart resize
        fg = smart_resize_person(fg, bg, person_scale)
        print(f"🔄 Resized person to: {fg.shape[1]}x{fg.shape[0]} (scale: {person_scale:.2f})")
        
        # Determine position
        if position == "auto":
            best_positions = analyze_background_for_placement(bg)
            x, y, score = best_positions[0]
            print(f"🎯 Auto-selected position: ({x}, {y}) with score: {score:.3f}")
            
            # Fine-tune for realism
            y = max(bg.shape[0] - fg.shape[0] - 30, y)
            
        elif isinstance(position, tuple):
            x, y = position
        else:
            y = bg.shape[0] - fg.shape[0] - 30
            if position == "left":
                x = 80
            elif position == "center":
                x = (bg.shape[1] - fg.shape[1]) // 2
            elif position == "right":
                x = bg.shape[1] - fg.shape[1] - 80
            else:
                x = 150
        
        # Ensure bounds
        y = max(0, min(y, bg.shape[0] - fg.shape[0]))
        x = max(0, min(x, bg.shape[1] - fg.shape[1]))
        
        print(f"📍 Final position: ({x}, {y})")
        
        # Extract alpha and RGB
        if fg.shape[2] == 4:
            alpha = fg[:, :, 3] / 255.0
            fg_rgb = fg[:, :, :3]
        else:
            alpha = np.ones(fg.shape[:2], dtype=np.float32)
            fg_rgb = fg
        
        # Create person mask
        person_mask = alpha > 0.1
        
        # Add shadow if requested
        if add_shadow:
            shadow = create_simple_shadow(person_mask.astype(np.uint8) * 255)
            
            # Apply shadow to background
            shadow_region = bg[y:y+fg.shape[0], x:x+fg.shape[1]]
            shadow_mask = shadow > 10
            if shadow_mask.any():
                shadow_region[shadow_mask] = (shadow_region[shadow_mask] * 0.75).astype(np.uint8)
                print("🌑 Added realistic shadow")
        
        # Composite person
        alpha_3d = alpha[:, :, np.newaxis]
        bg_region = bg[y:y+fg.shape[0], x:x+fg.shape[1]]
        blended = alpha_3d * fg_rgb + (1 - alpha_3d) * bg_region
        bg[y:y+fg.shape[0], x:x+fg.shape[1]] = blended.astype(np.uint8)
        
        # Save result
        cv2.imwrite(output_path, bg)
        print(f"✅ Created realistic composite: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Main function to create multiple realistic composites"""
    print("🎨 Enhanced Flam Project - Creating Realistic Composites\n")
    
    # Check if required files exist
    if not os.path.exists('assets/background.jpg'):
        print("❌ Background image not found!")
        return
    
    if not os.path.exists('assets/person_removed_ml.png'):
        print("❌ Person cutout not found! Run extract_person.py first.")
        return
    
    # Create multiple versions with different settings
    configs = [
        {
            'output': 'assets/final_composite_auto.jpg',
            'scale': 0.28,
            'position': 'auto',
            'shadow': True,
            'description': 'Auto-positioned with shadow'
        },
        {
            'output': 'assets/final_composite_center.jpg',
            'scale': 0.25,
            'position': 'center',
            'shadow': True,
            'description': 'Center-positioned with shadow'
        },
        {
            'output': 'assets/final_composite_left.jpg',
            'scale': 0.30,
            'position': 'left',
            'shadow': True,
            'description': 'Left-positioned with shadow'
        },
        {
            'output': 'assets/final_composite_large.jpg',
            'scale': 0.35,
            'position': 'auto',
            'shadow': True,
            'description': 'Large scale with auto-positioning'
        }
    ]
    
    successful = 0
    for i, config in enumerate(configs, 1):
        print(f"\n🖼️  Creating composite {i}/4: {config['description']}")
        success = create_realistic_composite(
            'assets/background.jpg',
            'assets/person_removed_ml.png',
            config['output'],
            person_scale=config['scale'],
            position=config['position'],
            add_shadow=config['shadow']
        )
        if success:
            successful += 1
    
    print(f"\n🎉 Successfully created {successful}/{len(configs)} realistic composites!")
    print("\n📁 Output files:")
    for config in configs:
        if os.path.exists(config['output']):
            print(f"   ✓ {config['output']} - {config['description']}")

if __name__ == "__main__":
    main()
