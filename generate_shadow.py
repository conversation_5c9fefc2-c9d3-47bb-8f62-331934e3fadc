import cv2
import numpy as np

def smart_resize(person_img, bg_img):
    """Resize person image to fit in background with padding if needed"""
    bg_h, bg_w = bg_img.shape[:2]
    person_h, person_w = person_img.shape[:2]
    
    # Calculate scaling factor (max 90% of background)
    scale = min(0.9 * bg_w/person_w, 0.9 * bg_h/person_h)
    
    if scale < 1:
        new_w = int(person_w * scale)
        new_h = int(person_h * scale)
        person_img = cv2.resize(person_img, (new_w, new_h), interpolation=cv2.INTER_AREA)
        print(f"Resized person from {person_w}x{person_h} to {new_w}x{new_h}")
    
    return person_img

def composite_images(bg_img, person_img, shadow, position=(100, 100)):
    """Safe composition with dimension checking"""
    bg_h, bg_w = bg_img.shape[:2]
    person_h, person_w = person_img.shape[:2]
    
    # Ensure position is within bounds
    x = max(0, min(position[0], bg_w - person_w))
    y = max(0, min(position[1], bg_h - person_h))
    
    # Create ROI with exact person dimensions
    roi = bg_img[y:y+person_h, x:x+person_w].copy()
    
    # Handle person mask (RGBA or RGB)
    if person_img.shape[2] == 4:
        person_mask = person_img[:,:,3] > 0
        person_rgb = person_img[:,:,:3]
    else:
        person_mask = cv2.cvtColor(person_img, cv2.COLOR_BGR2GRAY) > 0
        person_rgb = person_img
    
    # Ensure shadow matches ROI dimensions
    shadow_roi = shadow[y:y+person_h, x:x+person_w]
    if shadow_roi.shape != roi.shape[:2]:
        shadow_roi = cv2.resize(shadow_roi, (roi.shape[1], roi.shape[0]))
    
    # Apply shadow only where mask exists
    shadow_mask = shadow_roi > 10
    roi[shadow_mask] = roi[shadow_mask] * 0.7
    
    # Add person
    roi[person_mask] = person_rgb[person_mask]
    
    # Put back into background
    result = bg_img.copy()
    result[y:y+person_h, x:x+person_w] = roi
    
    return result

def main():
    # Load images
    bg_img = cv2.imread('assets/background.jpg')
    person_img = cv2.imread('assets/person_removed_ml.png', cv2.IMREAD_UNCHANGED)
    
    if bg_img is None or person_img is None:
        print("Error: Could not load images")
        return
    
    # Resize person if needed
    person_img = smart_resize(person_img, bg_img)
    
    # Create person mask
    if person_img.shape[2] == 4:
        person_mask = person_img[:,:,3]
    else:
        _, person_mask = cv2.threshold(cv2.cvtColor(person_img, cv2.COLOR_BGR2GRAY), 1, 255, cv2.THRESH_BINARY)
    
    # Detect light source (simplified for demo)
    light_pos = (bg_img.shape[1]//4, bg_img.shape[0]//4)  # Top-left by default
    
    # Generate simple shadow (for demonstration)
    shadow = np.zeros_like(person_mask, dtype=np.float32)
    offset_x = int(person_mask.shape[1] * 0.2)
    offset_y = int(person_mask.shape[0] * 0.1)
    M = np.float32([[1, 0, offset_x], [0, 1, offset_y]])
    shadow = cv2.warpAffine(person_mask.astype(np.float32), M,(person_mask.shape[1], person_mask.shape[0]))
    shadow = cv2.GaussianBlur(shadow, (25, 25), 0)
    shadow = (shadow * 0.7).astype(np.uint8)
    
    # Composite images (centered horizontally, near bottom)
    x_pos = (bg_img.shape[1] - person_img.shape[1]) // 2
    y_pos = bg_img.shape[0] - person_img.shape[0] - 50
    result = composite_images(bg_img, person_img, shadow, (x_pos, y_pos))
    
    # Save and show result
    cv2.imwrite('assets/final_result.jpg', result)
    cv2.imshow('Final Composition', result)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()