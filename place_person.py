import cv2
import numpy as np

def place_person(background_path, person_path, output_path, x=100, y=None):
    try:
        # Load images
        bg = cv2.imread(background_path)
        fg = cv2.imread(person_path, cv2.IMREAD_UNCHANGED)  # Must be PNG with alpha
        
        if bg is None or fg is None:
            raise ValueError("Could not load one or both images")
        
        # If foreground is larger than background, resize foreground
        if fg.shape[0] > bg.shape[0] or fg.shape[1] > bg.shape[1]:
            scale = min(bg.shape[0]/fg.shape[0], bg.shape[1]/fg.shape[1])
            new_width = int(fg.shape[1] * scale * 0.9)  # 90% of max possible size
            new_height = int(fg.shape[0] * scale * 0.9)
            fg = cv2.resize(fg, (new_width, new_height), interpolation=cv2.INTER_AREA)
            print(f"Resized person to {new_width}x{new_height}")
        
        # Set default y position (bottom-aligned if None)
        if y is None:
            y = bg.shape[0] - fg.shape[0]
        
        # Ensure we don't go out of bounds
        y = max(0, min(y, bg.shape[0] - fg.shape[0]))
        x = max(0, min(x, bg.shape[1] - fg.shape[1]))
        
        # Extract alpha channel
        if fg.shape[2] == 4:
            alpha = fg[:, :, 3] / 255.0
            fg = fg[:, :, :3]  # Remove alpha channel
        else:
            alpha = np.ones(fg.shape[:2], dtype=np.float32)
        
        # Composite images
        bg[y:y+fg.shape[0], x:x+fg.shape[1]] = (
            alpha[:, :, np.newaxis] * fg + 
            (1 - alpha[:, :, np.newaxis]) * bg[y:y+fg.shape[0], x:x+fg.shape[1]]
        )
        
        cv2.imwrite(output_path, bg)
        print(f"Successfully created composite at {output_path}")
        return True
    
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

# Usage example
success = place_person(
    'assets/background.jpg',
    'assets/person_cutout.png',
    'assets/output_composite.jpg',
    x=100  # Optional: specify y position or leave as None for bottom
)

if not success:
    print("Failed to create composite image")