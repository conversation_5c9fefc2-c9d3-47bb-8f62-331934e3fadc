import cv2
import numpy as np

def analyze_background_for_placement(bg_img):
    """Analyze background to find optimal placement positions"""
    height, width = bg_img.shape[:2]

    # Convert to grayscale for analysis
    gray = cv2.cvtColor(bg_img, cv2.COLOR_BGR2GRAY)

    # Find areas with less detail (smoother areas) for better placement
    blur = cv2.GaussianBlur(gray, (21, 21), 0)
    edges = cv2.Canny(blur, 50, 150)

    # Divide image into grid and find areas with fewer edges
    grid_size = 8
    h_step = height // grid_size
    w_step = width // grid_size

    best_positions = []

    for i in range(grid_size):
        for j in range(grid_size):
            y_start = i * h_step
            y_end = min((i + 1) * h_step, height)
            x_start = j * w_step
            x_end = min((j + 1) * w_step, width)

            # Count edges in this region
            region_edges = edges[y_start:y_end, x_start:x_end]
            edge_density = np.sum(region_edges) / (region_edges.shape[0] * region_edges.shape[1])

            # Prefer bottom areas and areas with lower edge density
            bottom_preference = (height - y_start) / height  # Higher for bottom areas
            score = bottom_preference * 0.7 + (1 - edge_density / 255) * 0.3

            best_positions.append((x_start, y_start, score))

    # Sort by score and return best positions
    best_positions.sort(key=lambda x: x[2], reverse=True)
    return best_positions[:3]  # Return top 3 positions

def smart_resize_person(fg, bg, target_scale=0.25):
    """Intelligently resize person based on background size and realistic proportions"""
    bg_height, bg_width = bg.shape[:2]
    fg_height, fg_width = fg.shape[:2]

    # Calculate realistic person size (typically 15-30% of background height)
    target_height = int(bg_height * target_scale)

    # Maintain aspect ratio
    aspect_ratio = fg_width / fg_height
    target_width = int(target_height * aspect_ratio)

    # Ensure person isn't too wide for the background
    if target_width > bg_width * 0.4:  # Max 40% of background width
        target_width = int(bg_width * 0.4)
        target_height = int(target_width / aspect_ratio)

    # Resize the person
    fg_resized = cv2.resize(fg, (target_width, target_height), interpolation=cv2.INTER_AREA)
    print(f"Resized person to {target_width}x{target_height} (scale: {target_scale:.2f})")

    return fg_resized

def place_person_smart(background_path, person_path, output_path, person_scale=0.25, position_preference="auto"):
    """
    Smart person placement with automatic positioning and realistic scaling

    Args:
        background_path: Path to background image
        person_path: Path to person cutout (PNG with transparency)
        output_path: Path for output composite
        person_scale: Scale of person relative to background height (0.1-0.4)
        position_preference: "auto", "left", "center", "right", or (x, y) tuple
    """
    try:
        # Load images
        bg = cv2.imread(background_path)
        fg = cv2.imread(person_path, cv2.IMREAD_UNCHANGED)

        if bg is None or fg is None:
            raise ValueError("Could not load one or both images")

        print(f"Background size: {bg.shape[1]}x{bg.shape[0]}")
        print(f"Person size: {fg.shape[1]}x{fg.shape[0]}")

        # Smart resize person
        fg = smart_resize_person(fg, bg, person_scale)

        # Determine placement position
        if position_preference == "auto":
            # Use intelligent positioning
            best_positions = analyze_background_for_placement(bg)
            x, y, score = best_positions[0]  # Use best position
            print(f"Auto-selected position: ({x}, {y}) with score: {score:.3f}")

            # Fine-tune position to be more realistic
            # Place person more towards bottom and ensure good spacing
            y = max(bg.shape[0] - fg.shape[0] - 20, y)  # At least 20px from bottom

        elif isinstance(position_preference, tuple):
            x, y = position_preference
        else:
            # Manual positioning based on preference
            y = bg.shape[0] - fg.shape[0] - 20  # 20px from bottom

            if position_preference == "left":
                x = 50
            elif position_preference == "center":
                x = (bg.shape[1] - fg.shape[1]) // 2
            elif position_preference == "right":
                x = bg.shape[1] - fg.shape[1] - 50
            else:
                x = 100  # Default

        # Ensure bounds
        y = max(0, min(y, bg.shape[0] - fg.shape[0]))
        x = max(0, min(x, bg.shape[1] - fg.shape[1]))

        print(f"Final placement position: ({x}, {y})")

        # Extract alpha channel and composite
        if fg.shape[2] == 4:
            alpha = fg[:, :, 3] / 255.0
            fg_rgb = fg[:, :, :3]
        else:
            alpha = np.ones(fg.shape[:2], dtype=np.float32)
            fg_rgb = fg

        # Create smooth blending
        alpha_3d = alpha[:, :, np.newaxis]

        # Composite the images
        bg_region = bg[y:y+fg.shape[0], x:x+fg.shape[1]]
        blended = alpha_3d * fg_rgb + (1 - alpha_3d) * bg_region
        bg[y:y+fg.shape[0], x:x+fg.shape[1]] = blended

        cv2.imwrite(output_path, bg)
        print(f"Successfully created realistic composite at {output_path}")
        return True

    except Exception as e:
        print(f"Error: {str(e)}")
        return False

# Legacy function for backward compatibility
def place_person(background_path, person_path, output_path, x=100, y=None):
    """Legacy function - use place_person_smart for better results"""
    if y is None:
        return place_person_smart(background_path, person_path, output_path,
                                person_scale=0.25, position_preference="auto")
    else:
        return place_person_smart(background_path, person_path, output_path,
                                person_scale=0.25, position_preference=(x, y))

# Usage examples with smart placement

# Example 1: Automatic intelligent placement (recommended)
print("Creating composite with automatic smart placement...")
success1 = place_person_smart(
    'assets/background.jpg',
    'assets/person_removed_ml.png',
    'assets/output_composite_smart.jpg',
    person_scale=0.3,  # Person will be 30% of background height
    position_preference="auto"
)

# Example 2: Manual positioning with different scales
print("\nCreating composite with center positioning...")
success2 = place_person_smart(
    'assets/background.jpg',
    'assets/person_removed_ml.png',
    'assets/output_composite_center.jpg',
    person_scale=0.25,  # Person will be 25% of background height
    position_preference="center"
)

# Example 3: Custom position
print("\nCreating composite with custom positioning...")
success3 = place_person_smart(
    'assets/background.jpg',
    'assets/person_removed_ml.png',
    'assets/output_composite_custom.jpg',
    person_scale=0.35,  # Person will be 35% of background height
    position_preference=(200, 300)  # Custom x, y position
)

# Legacy method (for backward compatibility)
print("\nCreating composite with legacy method...")
success4 = place_person(
    'assets/background.jpg',
    'assets/person_removed_ml.png',
    'assets/output_composite_legacy.jpg'
)

if success1 and success2 and success3:
    print("\n✅ All smart composite images created successfully!")
    print("Check the following files:")
    print("- assets/output_composite_smart.jpg (auto placement)")
    print("- assets/output_composite_center.jpg (center placement)")
    print("- assets/output_composite_custom.jpg (custom placement)")
else:
    print("❌ Some composite images failed to create")