from rembg import remove
from PIL import Image

def ml_background_removal(input_path, output_path):
    with open(input_path, 'rb') as i:
        with open(output_path, 'wb') as o:
            input = i.read()
            output = remove(input)
            o.write(output)
    return output_path

# This is the fastest high-quality method
ml_background_removal('assets/person.jpg', 'assets/person_removed_ml.png')