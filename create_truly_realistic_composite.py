#!/usr/bin/env python3
"""
TRULY REALISTIC Image Composition - Addresses actual visual realism issues
Focuses on lighting, color matching, perspective, and natural integration
"""

import cv2
import numpy as np
import os

def analyze_lighting_conditions(bg_img, person_region=None):
    """Analyze lighting conditions in background to match person lighting"""
    # Convert to LAB color space for better lighting analysis
    lab = cv2.cvtColor(bg_img, cv2.COLOR_BGR2LAB)
    l_channel = lab[:, :, 0]
    
    # Get overall brightness and contrast
    mean_brightness = np.mean(l_channel)
    brightness_std = np.std(l_channel)
    
    # Analyze shadows and highlights
    shadows = np.percentile(l_channel, 25)
    highlights = np.percentile(l_channel, 75)
    
    return {
        'brightness': mean_brightness,
        'contrast': brightness_std,
        'shadows': shadows,
        'highlights': highlights,
        'dynamic_range': highlights - shadows
    }

def match_person_to_background_lighting(person_img, bg_lighting, person_alpha):
    """Adjust person's lighting to match background"""
    # Convert person to LAB color space
    person_lab = cv2.cvtColor(person_img, cv2.COLOR_BGR2LAB).astype(np.float32)
    
    # Analyze person's current lighting
    person_mask = person_alpha > 0.1
    if not np.any(person_mask):
        return person_img
    
    person_l = person_lab[:, :, 0]
    person_brightness = np.mean(person_l[person_mask])
    person_contrast = np.std(person_l[person_mask])
    
    # Calculate adjustment factors
    brightness_factor = bg_lighting['brightness'] / person_brightness if person_brightness > 0 else 1.0
    contrast_factor = bg_lighting['contrast'] / person_contrast if person_contrast > 0 else 1.0
    
    # Limit extreme adjustments
    brightness_factor = np.clip(brightness_factor, 0.7, 1.4)
    contrast_factor = np.clip(contrast_factor, 0.8, 1.3)
    
    # Apply lighting adjustments
    person_lab[:, :, 0] = person_lab[:, :, 0] * brightness_factor
    person_lab[:, :, 0] = np.clip(person_lab[:, :, 0], 0, 100)
    
    # Adjust contrast
    mean_l = np.mean(person_lab[:, :, 0][person_mask])
    person_lab[:, :, 0] = mean_l + (person_lab[:, :, 0] - mean_l) * contrast_factor
    person_lab[:, :, 0] = np.clip(person_lab[:, :, 0], 0, 100)
    
    # Convert back to BGR
    adjusted_person = cv2.cvtColor(person_lab.astype(np.uint8), cv2.COLOR_LAB2BGR)
    return adjusted_person

def match_color_temperature(person_img, bg_img, person_alpha):
    """Match color temperature between person and background"""
    # Calculate average color temperature of background
    bg_mean = np.mean(bg_img.reshape(-1, 3), axis=0)
    
    # Calculate person's color temperature
    person_mask = person_alpha > 0.1
    if not np.any(person_mask):
        return person_img
    
    person_pixels = person_img[person_mask]
    person_mean = np.mean(person_pixels, axis=0)
    
    # Calculate color shift needed
    color_shift = bg_mean - person_mean
    
    # Apply subtle color temperature adjustment
    adjustment_strength = 0.3  # Subtle adjustment
    adjusted_person = person_img.astype(np.float32)
    
    for i in range(3):  # BGR channels
        adjusted_person[:, :, i] += color_shift[i] * adjustment_strength
    
    adjusted_person = np.clip(adjusted_person, 0, 255).astype(np.uint8)
    return adjusted_person

def create_realistic_shadow(person_alpha, bg_shape, person_pos, lighting_angle=45):
    """Create a realistic shadow based on lighting analysis"""
    h, w = person_alpha.shape
    bg_h, bg_w = bg_shape[:2]
    x, y = person_pos
    
    # Create shadow mask
    shadow_mask = (person_alpha > 0.1).astype(np.uint8) * 255
    
    # Calculate shadow offset based on lighting angle
    shadow_offset_x = int(np.cos(np.radians(lighting_angle)) * 20)
    shadow_offset_y = int(np.sin(np.radians(lighting_angle)) * 15)
    
    # Create shadow canvas
    shadow_canvas = np.zeros((bg_h, bg_w), dtype=np.uint8)
    
    # Place shadow with offset
    shadow_y = y + shadow_offset_y
    shadow_x = x + shadow_offset_x
    
    # Ensure shadow is within bounds
    if (shadow_y >= 0 and shadow_x >= 0 and 
        shadow_y + h <= bg_h and shadow_x + w <= bg_w):
        shadow_canvas[shadow_y:shadow_y+h, shadow_x:shadow_x+w] = shadow_mask
    
    # Blur shadow for realism
    shadow_canvas = cv2.GaussianBlur(shadow_canvas, (25, 25), 0)
    
    # Make shadow more realistic with gradient
    shadow_canvas = (shadow_canvas * 0.6).astype(np.uint8)
    
    return shadow_canvas

def add_edge_feathering(person_img, person_alpha, feather_size=3):
    """Add subtle edge feathering to blend person naturally"""
    # Create feathered alpha
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (feather_size*2+1, feather_size*2+1))
    feathered_alpha = cv2.morphologyEx(person_alpha, cv2.MORPH_ERODE, kernel)
    feathered_alpha = cv2.GaussianBlur(feathered_alpha, (feather_size*2+1, feather_size*2+1), 0)
    
    return feathered_alpha

def calculate_realistic_scale(bg_img, target_distance="medium"):
    """Calculate realistic person scale based on perspective"""
    bg_h, bg_w = bg_img.shape[:2]
    
    # Scale based on apparent distance
    scale_factors = {
        "close": 0.4,      # Close-up, person takes significant space
        "medium": 0.25,    # Normal conversation distance
        "far": 0.15,       # Person in background
        "very_far": 0.08   # Distant person
    }
    
    return scale_factors.get(target_distance, 0.25)

def find_realistic_ground_position(bg_img):
    """Find realistic ground/floor position for person placement"""
    h, w = bg_img.shape[:2]
    
    # Analyze bottom portion of image for ground plane
    bottom_region = bg_img[int(h*0.7):, :]
    
    # Convert to grayscale and find horizontal lines (potential ground)
    gray = cv2.cvtColor(bottom_region, cv2.COLOR_BGR2GRAY)
    edges = cv2.Canny(gray, 50, 150)
    
    # Find horizontal lines
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=w//4, maxLineGap=20)
    
    if lines is not None:
        # Find the most prominent horizontal line
        horizontal_lines = []
        for line in lines:
            x1, y1, x2, y2 = line[0]
            angle = abs(np.arctan2(y2-y1, x2-x1) * 180 / np.pi)
            if angle < 15:  # Nearly horizontal
                horizontal_lines.append(y1 + int(h*0.7))  # Adjust for bottom region offset
        
        if horizontal_lines:
            ground_y = int(np.median(horizontal_lines))
            return min(ground_y, h - 50)  # Ensure some margin from bottom
    
    # Default to bottom with margin
    return h - 80

def create_truly_realistic_composite(background_path, person_path, output_path, 
                                   distance="medium", position="auto"):
    """
    Create a TRULY realistic composite focusing on visual realism
    """
    try:
        print(f"🎨 Creating TRULY realistic composite...")
        
        # Load images
        bg = cv2.imread(background_path)
        person = cv2.imread(person_path, cv2.IMREAD_UNCHANGED)
        
        if bg is None or person is None:
            raise ValueError("Could not load images")
        
        print(f"📐 Background: {bg.shape[1]}x{bg.shape[0]}")
        print(f"👤 Person: {person.shape[1]}x{person.shape[0]}")
        
        # Extract alpha channel
        if person.shape[2] == 4:
            person_alpha = person[:, :, 3] / 255.0
            person_rgb = person[:, :, :3]
        else:
            person_alpha = np.ones(person.shape[:2], dtype=np.float32)
            person_rgb = person
        
        # 1. REALISTIC SCALING based on perspective
        realistic_scale = calculate_realistic_scale(bg, distance)
        bg_h, bg_w = bg.shape[:2]
        new_h = int(bg_h * realistic_scale)
        new_w = int(new_h * person.shape[1] / person.shape[0])
        
        person_rgb = cv2.resize(person_rgb, (new_w, new_h), interpolation=cv2.INTER_LANCZOS4)
        person_alpha = cv2.resize(person_alpha, (new_w, new_h), interpolation=cv2.INTER_LANCZOS4)
        
        print(f"🔄 Realistic resize: {new_w}x{new_h} (distance: {distance})")
        
        # 2. REALISTIC POSITIONING on ground plane
        if position == "auto":
            ground_y = find_realistic_ground_position(bg)
            y = ground_y - new_h
            x = (bg_w - new_w) // 2  # Center horizontally
        else:
            y = bg_h - new_h - 30
            x = (bg_w - new_w) // 2
        
        # Ensure bounds
        y = max(0, min(y, bg_h - new_h))
        x = max(0, min(x, bg_w - new_w))
        
        print(f"📍 Ground-based position: ({x}, {y})")
        
        # 3. LIGHTING ANALYSIS AND MATCHING
        bg_lighting = analyze_lighting_conditions(bg)
        print(f"💡 Background lighting - Brightness: {bg_lighting['brightness']:.1f}, Contrast: {bg_lighting['contrast']:.1f}")
        
        # 4. ADJUST PERSON LIGHTING TO MATCH BACKGROUND
        person_rgb = match_person_to_background_lighting(person_rgb, bg_lighting, person_alpha)
        print("🔆 Matched person lighting to background")
        
        # 5. COLOR TEMPERATURE MATCHING
        person_rgb = match_color_temperature(person_rgb, bg, person_alpha)
        print("🌡️  Matched color temperature")
        
        # 6. EDGE FEATHERING for natural blending
        person_alpha = add_edge_feathering(person_rgb, person_alpha, feather_size=2)
        print("✨ Added edge feathering")
        
        # 7. REALISTIC SHADOW CREATION
        shadow = create_realistic_shadow(person_alpha, bg.shape, (x, y))
        
        # Apply shadow to background
        shadow_3d = shadow[:, :, np.newaxis] / 255.0
        bg = bg.astype(np.float32)
        bg = bg * (1 - shadow_3d * 0.4)  # Darken background where shadow falls
        bg = bg.astype(np.uint8)
        print("🌑 Added realistic ground shadow")
        
        # 8. FINAL COMPOSITING with realistic blending
        person_region = bg[y:y+new_h, x:x+new_w]
        alpha_3d = person_alpha[:, :, np.newaxis]
        
        # Advanced blending
        blended = alpha_3d * person_rgb.astype(np.float32) + (1 - alpha_3d) * person_region.astype(np.float32)
        bg[y:y+new_h, x:x+new_w] = blended.astype(np.uint8)
        
        # Save result
        cv2.imwrite(output_path, bg)
        print(f"✅ TRULY realistic composite saved: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Create multiple truly realistic composites"""
    print("🎨 TRULY REALISTIC COMPOSITE GENERATOR")
    print("=" * 50)
    
    if not os.path.exists('assets/person_removed_ml.png'):
        print("❌ Person cutout not found!")
        return
    
    # Create different realistic versions
    configs = [
        {"output": "assets/REALISTIC_close.jpg", "distance": "close", "desc": "Close-up realistic"},
        {"output": "assets/REALISTIC_medium.jpg", "distance": "medium", "desc": "Medium distance realistic"},
        {"output": "assets/REALISTIC_far.jpg", "distance": "far", "desc": "Far distance realistic"},
    ]
    
    for config in configs:
        print(f"\n🎯 Creating: {config['desc']}")
        success = create_truly_realistic_composite(
            'assets/background.jpg',
            'assets/person_removed_ml.png',
            config['output'],
            distance=config['distance']
        )
        
        if success:
            print(f"✅ SUCCESS: {config['output']}")
        else:
            print(f"❌ FAILED: {config['output']}")
    
    print(f"\n🎉 REALISTIC COMPOSITES COMPLETE!")
    print("These should look truly realistic with proper lighting, shadows, and perspective!")

if __name__ == "__main__":
    main()
